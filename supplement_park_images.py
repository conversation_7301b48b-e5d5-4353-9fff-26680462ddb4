#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
园区图片补充脚本
从同城市园区中选取图片补充到不足9张的园区

功能：
1. 识别图片数量不足9张的园区
2. 从园区名称中推断城市信息
3. 从同城市其他园区中选取图片进行补充
4. 生成详细报告和SQL更新脚本

依赖：pip install pymysql
"""

import pymysql
import json
import sys
import random
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict


# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'your_username',   # 数据库用户名
    'password': 'your_password', # 数据库密码
    'database': 'erp_integration', # 数据库名
    'charset': 'utf8mb4',      # 字符集
    'autocommit': True         # 自动提交
}

# 中国主要城市列表（包括直辖市、省会城市、重点城市）
MAJOR_CITIES = [
    # 直辖市
    '北京', '上海', '天津', '重庆',
    # 省会城市
    '广州', '深圳', '杭州', '南京', '苏州', '成都', '武汉', '西安', '郑州', '济南',
    '沈阳', '长春', '哈尔滨', '石家庄', '太原', '呼和浩特', '兰州', '西宁', '银川',
    '乌鲁木齐', '拉萨', '昆明', '贵阳', '南宁', '海口', '长沙', '南昌', '福州',
    '合肥', '台北', '香港', '澳门',
    # 重点城市
    '青岛', '大连', '宁波', '厦门', '无锡', '佛山', '东莞', '泉州', '温州', '常州',
    '嘉兴', '金华', '绍兴', '台州', '湖州', '丽水', '衢州', '舟山', '珠海', '中山',
    '江门', '惠州', '汕头', '潮州', '揭阳', '梅州', '汕尾', '河源', '阳江', '清远',
    '韶关', '云浮', '茂名', '湛江', '肇庆', '南通', '徐州', '盐城', '淮安', '连云港',
    '泰州', '宿迁', '镇江', '扬州', '芜湖', '蚌埠', '淮南', '马鞍山', '淮北', '铜陵',
    '安庆', '黄山', '滁州', '阜阳', '宿州', '六安', '亳州', '池州', '宣城'
]

# 城市简称映射
CITY_ALIASES = {
    '京': '北京', '沪': '上海', '津': '天津', '渝': '重庆',
    '穗': '广州', '深': '深圳', '杭': '杭州', '宁': '南京',
    '蓉': '成都', '汉': '武汉', '西': '西安', '郑': '郑州'
}


def create_connection() -> Optional[pymysql.Connection]:
    """创建数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return connection
    except pymysql.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接过程中发生未知错误: {e}")
        return None


def close_connection(connection: pymysql.Connection) -> None:
    """关闭数据库连接"""
    if connection:
        try:
            connection.close()
            print("✅ 数据库连接已关闭")
        except Exception as e:
            print(f"⚠️ 关闭连接时发生错误: {e}")


def extract_city_from_park_name(park_name: str) -> Optional[str]:
    """
    从园区名称中提取城市信息
    
    Args:
        park_name: 园区名称
        
    Returns:
        str: 识别出的城市名称，未识别返回 None
    """
    if not park_name:
        return None
    
    # 首先检查城市简称
    for alias, full_name in CITY_ALIASES.items():
        if alias in park_name:
            return full_name
    
    # 检查完整城市名称（按长度倒序，优先匹配长名称）
    sorted_cities = sorted(MAJOR_CITIES, key=len, reverse=True)
    for city in sorted_cities:
        if city in park_name:
            return city
    
    # 使用正则表达式匹配地名模式
    # 匹配 "XX市"、"XX区"、"XX县" 等模式
    patterns = [
        r'([^省]{2,4}市)',  # XX市
        r'([^市]{2,4}区)',  # XX区  
        r'([^区]{2,4}县)',  # XX县
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, park_name)
        if matches:
            potential_city = matches[0].replace('市', '').replace('区', '').replace('县', '')
            # 验证是否为已知城市的一部分
            for city in MAJOR_CITIES:
                if potential_city in city or city in potential_city:
                    return city
    
    return None


def get_parks_with_insufficient_images(connection: pymysql.Connection) -> List[Dict[str, Any]]:
    """
    查询图片数量不足9张的园区
    
    Args:
        connection: 数据库连接对象
        
    Returns:
        List[Dict]: 图片不足的园区列表
    """
    sql_query = """
    SELECT 
        id,
        park_name,
        images_json,
        CASE 
            WHEN images_json IS NULL THEN 0
            WHEN JSON_VALID(images_json) = 0 THEN 0
            WHEN JSON_TYPE(images_json) = 'ARRAY' THEN JSON_LENGTH(images_json)
            WHEN JSON_TYPE(images_json) = 'OBJECT' AND JSON_CONTAINS_PATH(images_json, 'one', '$.images') 
                 THEN JSON_LENGTH(images_json, '$.images')
            ELSE 0
        END as image_count,
        created_at,
        updated_at
    FROM park_gallery_unified 
    WHERE (
        images_json IS NULL 
        OR JSON_VALID(images_json) = 0 
        OR (JSON_TYPE(images_json) = 'ARRAY' AND JSON_LENGTH(images_json) < 9)
        OR (JSON_TYPE(images_json) = 'OBJECT' AND JSON_CONTAINS_PATH(images_json, 'one', '$.images') 
            AND JSON_LENGTH(images_json, '$.images') < 9)
    )
    ORDER BY image_count ASC, park_name ASC;
    """
    
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(sql_query)
            results = cursor.fetchall()
            print(f"🔍 找到 {len(results)} 个图片不足9张的园区")
            return results
    except Exception as e:
        print(f"❌ 查询图片不足园区失败: {e}")
        return []


def get_parks_with_sufficient_images_by_city(connection: pymysql.Connection, city: str) -> List[Dict[str, Any]]:
    """
    查询指定城市中图片充足的园区（用于补充图片）
    
    Args:
        connection: 数据库连接对象
        city: 城市名称
        
    Returns:
        List[Dict]: 该城市图片充足的园区列表
    """
    sql_query = """
    SELECT 
        id,
        park_name,
        images_json,
        CASE 
            WHEN JSON_TYPE(images_json) = 'ARRAY' THEN JSON_LENGTH(images_json)
            WHEN JSON_TYPE(images_json) = 'OBJECT' AND JSON_CONTAINS_PATH(images_json, 'one', '$.images') 
                 THEN JSON_LENGTH(images_json, '$.images')
            ELSE 0
        END as image_count
    FROM park_gallery_unified 
    WHERE park_name LIKE %s
    AND (
        (JSON_TYPE(images_json) = 'ARRAY' AND JSON_LENGTH(images_json) > 0)
        OR (JSON_TYPE(images_json) = 'OBJECT' AND JSON_CONTAINS_PATH(images_json, 'one', '$.images') 
            AND JSON_LENGTH(images_json, '$.images') > 0)
    )
    ORDER BY image_count DESC;
    """
    
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute(sql_query, (f'%{city}%',))
            results = cursor.fetchall()
            return results
    except Exception as e:
        print(f"❌ 查询城市 {city} 的园区失败: {e}")
        return []


def parse_images_json(images_json: Any) -> List[Dict[str, Any]]:
    """
    解析 images_json 字段，返回图片列表
    
    Args:
        images_json: JSON 数据
        
    Returns:
        List[Dict]: 图片信息列表
    """
    if not images_json:
        return []
    
    try:
        if isinstance(images_json, str):
            data = json.loads(images_json)
        else:
            data = images_json
        
        if isinstance(data, list):
            return data
        elif isinstance(data, dict) and 'images' in data:
            return data['images']
        else:
            return []
    except (json.JSONDecodeError, TypeError):
        return []


def select_supplement_images(target_park: Dict[str, Any], source_parks: List[Dict[str, Any]], 
                           needed_count: int) -> List[Dict[str, Any]]:
    """
    从源园区中选择图片来补充目标园区
    
    Args:
        target_park: 目标园区信息
        source_parks: 源园区列表
        needed_count: 需要补充的图片数量
        
    Returns:
        List[Dict]: 选中的补充图片列表
    """
    # 获取目标园区现有图片的URL，避免重复
    existing_images = parse_images_json(target_park['images_json'])
    existing_urls = {img.get('url', '') for img in existing_images if isinstance(img, dict)}
    
    # 收集所有可用的补充图片
    available_images = []
    for source_park in source_parks:
        if source_park['id'] == target_park['id']:  # 跳过自己
            continue
            
        source_images = parse_images_json(source_park['images_json'])
        for img in source_images:
            if isinstance(img, dict) and img.get('url') and img['url'] not in existing_urls:
                # 添加源园区信息
                supplement_img = img.copy()
                supplement_img['source_park_id'] = source_park['id']
                supplement_img['source_park_name'] = source_park['park_name']
                available_images.append(supplement_img)
    
    # 如果可用图片不足
    if len(available_images) < needed_count:
        print(f"⚠️ 园区 {target_park['park_name']} 同城市可用补充图片不足: 需要{needed_count}张，可用{len(available_images)}张")
        needed_count = len(available_images)
    
    # 智能选择图片：优先选择不同类型的图片
    selected_images = []
    used_fields = {img.get('original_field', '') for img in existing_images if isinstance(img, dict)}
    
    # 首先选择不同类型的图片
    for img in available_images:
        if len(selected_images) >= needed_count:
            break
        field = img.get('original_field', '')
        if field and field not in used_fields:
            selected_images.append(img)
            used_fields.add(field)
    
    # 如果还需要更多图片，随机选择剩余的
    remaining_images = [img for img in available_images if img not in selected_images]
    random.shuffle(remaining_images)
    
    while len(selected_images) < needed_count and remaining_images:
        selected_images.append(remaining_images.pop())
    
    return selected_images


def generate_supplement_report(supplement_plans: List[Dict[str, Any]], unrecognized_parks: List[Dict[str, Any]]) -> None:
    """
    生成图片补充计划报告

    Args:
        supplement_plans: 补充计划列表
        unrecognized_parks: 无法识别城市的园区列表
    """
    print("\n" + "="*80)
    print("📋 图片补充计划报告")
    print("="*80)

    # 统计信息
    total_parks = len(supplement_plans)
    total_images = sum(len(plan['selected_images']) for plan in supplement_plans)
    cities = set(plan['city'] for plan in supplement_plans)

    print(f"\n📊 总体统计:")
    print(f"   涉及城市数量: {len(cities)}")
    print(f"   需要补充的园区数量: {total_parks}")
    print(f"   计划补充的图片总数: {total_images}")

    # 按城市分组显示
    city_plans = defaultdict(list)
    for plan in supplement_plans:
        city_plans[plan['city']].append(plan)

    for city, plans in city_plans.items():
        print(f"\n🏙️ 城市: {city}")
        print(f"   需要补充的园区: {len(plans)} 个")

        for i, plan in enumerate(plans, 1):
            park = plan['target_park']
            images = plan['selected_images']

            print(f"\n   {i}. 园区: {park['park_name']}")
            print(f"      ID: {park['id']}")
            print(f"      当前图片数量: {park['image_count']}")
            print(f"      需要补充: {plan['needed_count']} 张")
            print(f"      实际补充: {len(images)} 张")

            if images:
                print(f"      补充图片来源:")
                source_parks = set()
                for img in images:
                    source_parks.add(f"{img['source_park_name']} (ID: {img['source_park_id']})")

                for source in source_parks:
                    source_count = sum(1 for img in images if f"{img['source_park_name']} (ID: {img['source_park_id']})" == source)
                    print(f"        - {source}: {source_count} 张图片")

    # 显示无法识别城市的园区
    if unrecognized_parks:
        print(f"\n⚠️ 无法识别城市的园区 ({len(unrecognized_parks)} 个):")
        for park in unrecognized_parks:
            print(f"   - {park['park_name']} (ID: {park['id']}, 当前图片: {park['image_count']} 张)")
        print("   建议: 手动检查园区名称或添加城市信息")


def generate_update_sql(supplement_plans: List[Dict[str, Any]]) -> None:
    """
    生成更新SQL脚本

    Args:
        supplement_plans: 补充计划列表
    """
    if not supplement_plans:
        return

    print("\n" + "="*80)
    print("📝 SQL更新脚本")
    print("="*80)

    sql_file_content = []
    sql_file_content.append("-- 园区图片补充SQL脚本")
    sql_file_content.append("-- 生成时间: " + str(datetime.now()))
    sql_file_content.append("-- 注意: 执行前请备份数据库!")
    sql_file_content.append("")
    sql_file_content.append("-- 开始事务")
    sql_file_content.append("START TRANSACTION;")
    sql_file_content.append("")

    for plan in supplement_plans:
        park = plan['target_park']
        selected_images = plan['selected_images']

        # 获取现有图片
        existing_images = parse_images_json(park['images_json'])

        # 合并图片
        all_images = existing_images + selected_images

        # 构建新的JSON结构
        if park['images_json'] and isinstance(park['images_json'], dict) and 'images' in park['images_json']:
            # 对象格式
            new_json = {"images": all_images}
        else:
            # 数组格式
            new_json = all_images

        new_json_str = json.dumps(new_json, ensure_ascii=False, separators=(',', ':'))

        sql_file_content.append(f"-- 更新园区: {park['park_name']} (ID: {park['id']})")
        sql_file_content.append(f"-- 当前图片数量: {park['image_count']}, 补充: {len(selected_images)} 张")
        sql_file_content.append("UPDATE park_gallery_unified")
        sql_file_content.append(f"SET images_json = '{new_json_str}',")
        sql_file_content.append("    updated_at = NOW()")
        sql_file_content.append(f"WHERE id = {park['id']};")
        sql_file_content.append("")

    sql_file_content.append("-- 提交事务")
    sql_file_content.append("COMMIT;")
    sql_file_content.append("")
    sql_file_content.append("-- 如果出现错误，可以回滚:")
    sql_file_content.append("-- ROLLBACK;")

    # 保存SQL文件
    sql_filename = "park_images_supplement.sql"
    try:
        with open(sql_filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(sql_file_content))
        print(f"✅ SQL脚本已保存到: {sql_filename}")
        print("⚠️ 执行前请务必备份数据库!")
    except Exception as e:
        print(f"❌ 保存SQL文件失败: {e}")
        print("\n📝 SQL内容:")
        print('\n'.join(sql_file_content))


def main():
    """主函数"""
    print("🚀 开始园区图片补充任务...")
    print(f"📋 目标数据库: {DB_CONFIG['database']}")
    print(f"📋 目标表: park_gallery_unified")
    print("-" * 60)
    
    # 创建数据库连接
    connection = create_connection()
    if not connection:
        print("❌ 无法建立数据库连接，程序退出")
        sys.exit(1)
    
    try:
        # 查询图片不足的园区
        insufficient_parks = get_parks_with_insufficient_images(connection)
        if not insufficient_parks:
            print("✅ 所有园区图片都已充足，无需补充")
            return
        
        print(f"\n📊 分析结果:")
        print(f"   找到 {len(insufficient_parks)} 个需要补充图片的园区")
        
        # 按城市分组处理
        city_groups = defaultdict(list)
        unrecognized_parks = []
        
        for park in insufficient_parks:
            city = extract_city_from_park_name(park['park_name'])
            if city:
                city_groups[city].append(park)
            else:
                unrecognized_parks.append(park)
        
        print(f"   识别出 {len(city_groups)} 个城市")
        print(f"   无法识别城市的园区: {len(unrecognized_parks)} 个")
        
        # 处理每个城市的园区
        supplement_plans = []
        
        for city, parks in city_groups.items():
            print(f"\n🏙️ 处理城市: {city} ({len(parks)} 个园区)")
            
            # 查询该城市的其他园区
            source_parks = get_parks_with_sufficient_images_by_city(connection, city)
            print(f"   找到 {len(source_parks)} 个可作为图片源的园区")
            
            if not source_parks:
                print(f"   ⚠️ 城市 {city} 没有可用的图片源园区")
                continue
            
            # 为每个园区制定补充计划
            for park in parks:
                needed_count = 9 - park['image_count']
                selected_images = select_supplement_images(park, source_parks, needed_count)
                
                if selected_images:
                    supplement_plans.append({
                        'target_park': park,
                        'city': city,
                        'needed_count': needed_count,
                        'selected_images': selected_images
                    })
                    print(f"   ✅ 园区 {park['park_name']}: 计划补充 {len(selected_images)} 张图片")
                else:
                    print(f"   ❌ 园区 {park['park_name']}: 无法找到合适的补充图片")
        
        # 输出补充计划报告
        if supplement_plans:
            generate_supplement_report(supplement_plans, unrecognized_parks)
            generate_update_sql(supplement_plans)
        else:
            print("\n❌ 没有生成任何补充计划")
            
    finally:
        close_connection(connection)
    
    print("\n✅ 程序执行完成")


if __name__ == "__main__":
    # 检查依赖
    try:
        import pymysql
    except ImportError:
        print("❌ 缺少依赖包 pymysql")
        print("请运行: pip install pymysql")
        sys.exit(1)
    
    main()
