#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询图片数量不足9张的园区脚本
使用 pymysql + 纯SQL 方案

功能：从 MySQL 数据库 erp_integration.park_gallery_unified 表中
查询 images_json 字段图片数量少于9张的园区记录

依赖：pip install pymysql
"""

import pymysql
import json
import sys
from typing import List, Dict, Any, Optional


# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'your_username',   # 数据库用户名
    'password': 'your_password', # 数据库密码
    'database': 'erp_integration', # 数据库名
    'charset': 'utf8mb4',      # 字符集
    'autocommit': True         # 自动提交
}


def create_connection() -> Optional[pymysql.Connection]:
    """
    创建数据库连接
    
    Returns:
        pymysql.Connection: 数据库连接对象，连接失败时返回 None
    """
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return connection
    except pymysql.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接过程中发生未知错误: {e}")
        return None


def close_connection(connection: pymysql.Connection) -> None:
    """
    关闭数据库连接
    
    Args:
        connection: 数据库连接对象
    """
    if connection:
        try:
            connection.close()
            print("✅ 数据库连接已关闭")
        except Exception as e:
            print(f"⚠️ 关闭连接时发生错误: {e}")


def query_parks_with_insufficient_images(connection: pymysql.Connection) -> List[Dict[str, Any]]:
    """
    查询图片数量不足9张的园区
    
    Args:
        connection: 数据库连接对象
        
    Returns:
        List[Dict]: 查询结果列表
    """
    
    # SQL 查询语句 - 使用 MySQL JSON 函数处理 images_json 字段
    sql_query = """
    SELECT 
        id,
        park_name,
        images_json,
        CASE 
            WHEN images_json IS NULL THEN 0
            WHEN JSON_VALID(images_json) = 0 THEN 0
            ELSE JSON_LENGTH(images_json)
        END as image_count,
        created_at,
        updated_at
    FROM park_gallery_unified 
    WHERE (
        images_json IS NULL 
        OR JSON_VALID(images_json) = 0 
        OR JSON_LENGTH(images_json) < 9
    )
    ORDER BY image_count ASC, park_name ASC;
    """
    
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            print("🔍 执行查询...")
            cursor.execute(sql_query)
            results = cursor.fetchall()
            print(f"✅ 查询完成，找到 {len(results)} 条记录")
            return results
            
    except pymysql.Error as e:
        print(f"❌ SQL 查询执行失败: {e}")
        return []
    except Exception as e:
        print(f"❌ 查询过程中发生未知错误: {e}")
        return []


def format_and_display_results(results: List[Dict[str, Any]]) -> None:
    """
    格式化并显示查询结果
    
    Args:
        results: 查询结果列表
    """
    if not results:
        print("📝 没有找到图片数量不足9张的园区")
        return
    
    print("\n" + "="*80)
    print("📊 图片数量不足9张的园区列表")
    print("="*80)
    
    for i, record in enumerate(results, 1):
        park_id = record['id']
        park_name = record['park_name']
        image_count = record['image_count']
        created_at = record['created_at']
        updated_at = record['updated_at']
        
        print(f"\n{i}. 园区信息:")
        print(f"   ID: {park_id}")
        print(f"   名称: {park_name}")
        print(f"   图片数量: {image_count} 张")
        print(f"   创建时间: {created_at}")
        print(f"   更新时间: {updated_at}")
        
        # 显示 images_json 的简要信息
        images_json = record['images_json']
        if images_json is None:
            print(f"   图片数据: NULL")
        else:
            try:
                # 尝试解析 JSON 以显示更多信息
                if isinstance(images_json, str):
                    json_data = json.loads(images_json)
                else:
                    json_data = images_json
                    
                if isinstance(json_data, list):
                    print(f"   图片数据: 数组格式，包含 {len(json_data)} 个元素")
                elif isinstance(json_data, dict):
                    print(f"   图片数据: 对象格式，包含字段: {list(json_data.keys())}")
                else:
                    print(f"   图片数据: {type(json_data).__name__} 类型")
            except (json.JSONDecodeError, TypeError):
                print(f"   图片数据: 无效的 JSON 格式")
        
        print("-" * 60)
    
    print(f"\n📈 统计信息:")
    print(f"   总计找到 {len(results)} 个园区图片数量不足9张")
    
    # 按图片数量分组统计
    count_stats = {}
    for record in results:
        count = record['image_count']
        count_stats[count] = count_stats.get(count, 0) + 1
    
    print(f"   图片数量分布:")
    for count in sorted(count_stats.keys()):
        print(f"     {count} 张图片: {count_stats[count]} 个园区")


def main():
    """
    主函数
    """
    print("🚀 开始查询图片数量不足9张的园区...")
    print(f"📋 目标数据库: {DB_CONFIG['database']}")
    print(f"📋 目标表: park_gallery_unified")
    print("-" * 50)
    
    # 创建数据库连接
    connection = create_connection()
    if not connection:
        print("❌ 无法建立数据库连接，程序退出")
        sys.exit(1)
    
    try:
        # 执行查询
        results = query_parks_with_insufficient_images(connection)
        
        # 显示结果
        format_and_display_results(results)
        
    finally:
        # 确保连接被正确关闭
        close_connection(connection)
    
    print("\n✅ 程序执行完成")


if __name__ == "__main__":
    # 检查是否安装了 pymysql
    try:
        import pymysql
    except ImportError:
        print("❌ 缺少依赖包 pymysql")
        print("请运行: pip install pymysql")
        sys.exit(1)
    
    main()
