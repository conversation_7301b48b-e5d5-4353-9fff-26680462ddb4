#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全SQL执行脚本
用于安全地执行 park_images_supplement.sql 文件

功能：
1. 读取和解析SQL文件
2. 执行前预览和确认
3. 自动备份相关数据
4. 安全执行SQL更新
5. 详细的执行日志
6. 错误处理和回滚机制

依赖：pip install pymysql
"""

import pymysql
import json
import sys
import os
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple


# 数据库连接配置
DB_CONFIG = {
    'host': '**********',        # 数据库主机地址
    'port': 3306,              # 数据库端口
    'user': 'root',   # 数据库用户名
    'password': 'LianDodmx_03', # 数据库密码
    'database': 'erp_integration', # 数据库名
    'charset': 'utf8mb4',      # 字符集
    'autocommit': True         # 自动提交
}

# SQL文件路径
SQL_FILE_PATH = 'park_images_supplement.sql'


def create_connection() -> Optional[pymysql.Connection]:
    """创建数据库连接"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return connection
    except pymysql.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None
    except Exception as e:
        print(f"❌ 连接过程中发生未知错误: {e}")
        return None


def close_connection(connection: pymysql.Connection) -> None:
    """关闭数据库连接"""
    if connection:
        try:
            connection.close()
            print("✅ 数据库连接已关闭")
        except Exception as e:
            print(f"⚠️ 关闭连接时发生错误: {e}")


def read_sql_file(file_path: str) -> Optional[str]:
    """
    读取SQL文件内容
    
    Args:
        file_path: SQL文件路径
        
    Returns:
        str: SQL文件内容，读取失败返回None
    """
    try:
        if not os.path.exists(file_path):
            print(f"❌ SQL文件不存在: {file_path}")
            return None
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        print(f"✅ 成功读取SQL文件: {file_path}")
        return content
        
    except Exception as e:
        print(f"❌ 读取SQL文件失败: {e}")
        return None


def parse_sql_updates(sql_content: str) -> List[Dict[str, Any]]:
    """
    解析SQL文件中的UPDATE语句

    Args:
        sql_content: SQL文件内容

    Returns:
        List[Dict]: 解析出的UPDATE操作列表
    """
    updates = []

    # 使用更灵活的正则表达式匹配UPDATE语句和注释
    # 支持可选的"补充来源"行
    pattern = r'-- 更新园区: (.+?) \(ID: (\d+)\)\s*\n-- 当前图片数量: (\d+), 补充: (\d+) 张\s*(?:\n-- 补充来源:.*?)?\s*\n(UPDATE.*?WHERE id = \2;)'

    matches = re.findall(pattern, sql_content, re.DOTALL | re.IGNORECASE)

    for match in matches:
        park_name, park_id, current_count, supplement_count, update_sql = match

        updates.append({
            'park_name': park_name.strip(),
            'park_id': int(park_id),
            'current_count': int(current_count),
            'supplement_count': int(supplement_count),
            'update_sql': update_sql.strip()
        })

    return updates


def preview_updates(updates: List[Dict[str, Any]]) -> None:
    """
    预览将要执行的更新操作
    
    Args:
        updates: 更新操作列表
    """
    print("\n" + "="*80)
    print("📋 SQL执行预览")
    print("="*80)
    
    print(f"\n📊 总体统计:")
    print(f"   将要更新的园区数量: {len(updates)}")
    print(f"   总计补充图片数量: {sum(u['supplement_count'] for u in updates)}")
    
    print(f"\n📝 详细操作列表:")
    for i, update in enumerate(updates, 1):
        print(f"\n   {i}. 园区: {update['park_name']}")
        print(f"      ID: {update['park_id']}")
        print(f"      当前图片: {update['current_count']} 张")
        print(f"      补充图片: {update['supplement_count']} 张")
        print(f"      更新后: {update['current_count'] + update['supplement_count']} 张")


def backup_park_data(connection: pymysql.Connection, park_ids: List[int]) -> bool:
    """
    备份将要更新的园区数据
    
    Args:
        connection: 数据库连接
        park_ids: 园区ID列表
        
    Returns:
        bool: 备份是否成功
    """
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"park_backup_{timestamp}.sql"
        
        print(f"\n🔄 正在备份数据到: {backup_file}")
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            backup_content = []
            backup_content.append(f"-- 园区数据备份文件")
            backup_content.append(f"-- 备份时间: {datetime.now()}")
            backup_content.append(f"-- 备份的园区ID: {park_ids}")
            backup_content.append("")
            
            for park_id in park_ids:
                cursor.execute(
                    "SELECT id, park_name, images_json, created_at, updated_at FROM park_gallery_unified WHERE id = %s",
                    (park_id,)
                )
                result = cursor.fetchone()
                
                if result:
                    images_json_str = json.dumps(result['images_json']) if result['images_json'] else 'NULL'
                    backup_content.append(f"-- 备份园区: {result['park_name']} (ID: {park_id})")
                    backup_content.append("UPDATE park_gallery_unified SET")
                    backup_content.append(f"  images_json = '{images_json_str}',")
                    backup_content.append(f"  updated_at = '{result['updated_at']}'")
                    backup_content.append(f"WHERE id = {park_id};")
                    backup_content.append("")
        
        # 保存备份文件
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(backup_content))
        
        print(f"✅ 数据备份完成: {backup_file}")
        return True
        
    except Exception as e:
        print(f"❌ 数据备份失败: {e}")
        return False


def execute_updates_safely(connection: pymysql.Connection, updates: List[Dict[str, Any]]) -> bool:
    """
    安全执行更新操作
    
    Args:
        connection: 数据库连接
        updates: 更新操作列表
        
    Returns:
        bool: 执行是否成功
    """
    try:
        print(f"\n🚀 开始执行SQL更新...")
        
        # 开始事务
        connection.begin()
        print("✅ 事务已开始")
        
        success_count = 0
        
        with connection.cursor() as cursor:
            for i, update in enumerate(updates, 1):
                try:
                    print(f"\n📝 执行第 {i}/{len(updates)} 个更新:")
                    print(f"   园区: {update['park_name']} (ID: {update['park_id']})")
                    
                    # 执行UPDATE语句
                    cursor.execute(update['update_sql'])
                    affected_rows = cursor.rowcount
                    
                    if affected_rows > 0:
                        print(f"   ✅ 更新成功，影响行数: {affected_rows}")
                        success_count += 1
                    else:
                        print(f"   ⚠️ 更新完成，但没有影响任何行")
                        
                except Exception as e:
                    print(f"   ❌ 更新失败: {e}")
                    raise e
        
        # 提交事务
        connection.commit()
        print(f"\n✅ 所有更新执行完成!")
        print(f"   成功更新: {success_count}/{len(updates)} 个园区")
        print("✅ 事务已提交")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {e}")
        try:
            connection.rollback()
            print("🔄 事务已回滚，数据库状态已恢复")
        except Exception as rollback_error:
            print(f"❌ 回滚失败: {rollback_error}")
        return False


def get_user_confirmation() -> bool:
    """获取用户确认"""
    while True:
        response = input("\n❓ 确认执行以上更新操作吗？(y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            return True
        elif response in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/yes/是 或 n/no/否")


def main():
    """主函数"""
    print("🚀 安全SQL执行工具")
    print(f"📋 目标SQL文件: {SQL_FILE_PATH}")
    print(f"📋 目标数据库: {DB_CONFIG['database']}")
    print("-" * 60)
    
    # 检查SQL文件是否存在
    if not os.path.exists(SQL_FILE_PATH):
        print(f"❌ SQL文件不存在: {SQL_FILE_PATH}")
        print("请确保 park_images_supplement.sql 文件在当前目录中")
        sys.exit(1)
    
    # 读取SQL文件
    sql_content = read_sql_file(SQL_FILE_PATH)
    if not sql_content:
        sys.exit(1)
    
    # 解析UPDATE操作
    updates = parse_sql_updates(sql_content)
    if not updates:
        print("❌ 未找到有效的UPDATE操作")
        sys.exit(1)
    
    # 预览更新操作
    preview_updates(updates)
    
    # 获取用户确认
    if not get_user_confirmation():
        print("❌ 用户取消操作")
        sys.exit(0)
    
    # 创建数据库连接
    connection = create_connection()
    if not connection:
        sys.exit(1)
    
    try:
        # 备份数据
        park_ids = [update['park_id'] for update in updates]
        if not backup_park_data(connection, park_ids):
            print("❌ 数据备份失败，为安全起见，取消执行")
            sys.exit(1)
        
        # 执行更新
        success = execute_updates_safely(connection, updates)
        
        if success:
            print("\n🎉 所有操作执行成功!")
            print("💡 建议: 可以运行查询脚本验证更新结果")
        else:
            print("\n❌ 执行失败，请检查错误信息")
            
    finally:
        close_connection(connection)
    
    print("\n✅ 程序执行完成")


if __name__ == "__main__":
    # 检查依赖
    try:
        import pymysql
    except ImportError:
        print("❌ 缺少依赖包 pymysql")
        print("请运行: pip install pymysql")
        sys.exit(1)
    
    main()
